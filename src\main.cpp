#include <Arduino.h>
#include <SPI.h>
#include <Ethernet.h>

#define ETH_MISO 13
#define ETH_SCK 12
#define ETH_MOSI 11
#define ETH_CS 10
// #define ETH_RST 15 // 9 gateway
#define ETH_RST 9 // 15 kaifaban
#define ETH_INT 14

#define PIN_SPI_SS ETH_CS
#define PIN_ETHERNET_RESET ETH_RST

void getMacAddr(uint8_t *dmac)
{
  assert(esp_efuse_mac_get_default(dmac) == ESP_OK);
}


void setup() {
  Serial0.begin(115200);
  // initialize SX1262 with default settings
  Serial0.print(F("Ota thread start\n"));

  pinMode(PIN_ETHERNET_RESET, OUTPUT);
  digitalWrite(PIN_ETHERNET_RESET, LOW); // Reset Time.
  delay(100);
  digitalWrite(PIN_ETHERNET_RESET, HIGH); // Reset Time.

  Ethernet.init(PIN_SPI_SS);
  SPI.begin(ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  Serial0.printf("SPI.begin(SCK=%d, MISO=%d, MOSI=%d, NSS=%d\n", ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  SPI.setFrequency(4000000);
  
  int status = 0;

  uint8_t mac[6];
  getMacAddr(mac); 
  mac[0] &= 0xfe;  

  Serial0.printf("Start Ethernet DHCP\n");
  status = Ethernet.begin(mac);

  if (status == 0) {
    if (Ethernet.hardwareStatus() == EthernetNoHardware) {
        Serial0.printf("Ethernet shield was not found\n");
        return;
    } else if (Ethernet.linkStatus() == LinkOFF) {
        Serial0.printf("Ethernet cable is not connected\n");
        return;
    } else {
        Serial0.printf("Unknown Ethernet error\n");
        return;
    }
} else {
    Serial0.printf("Local IP %u.%u.%u.%u\n", Ethernet.localIP()[0], Ethernet.localIP()[1], Ethernet.localIP()[2],
             Ethernet.localIP()[3]);
    Serial0.printf("Subnet Mask %u.%u.%u.%u\n", Ethernet.subnetMask()[0], Ethernet.subnetMask()[1], Ethernet.subnetMask()[2],
             Ethernet.subnetMask()[3]);
    Serial0.printf("Gateway IP %u.%u.%u.%u\n", Ethernet.gatewayIP()[0], Ethernet.gatewayIP()[1], Ethernet.gatewayIP()[2],
             Ethernet.gatewayIP()[3]);
    Serial0.printf("DNS Server IP %u.%u.%u.%u\n", Ethernet.dnsServerIP()[0], Ethernet.dnsServerIP()[1], Ethernet.dnsServerIP()[2],
             Ethernet.dnsServerIP()[3]);
}




}

void loop() {

}
