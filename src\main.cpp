#include <Arduino.h>
#include <SPI.h>
#include <Ethernet.h>
#include <Update.h>
#include <WiFi.h>
#include <WebServer.h>

#define ETH_MISO 13
#define ETH_SCK 12
#define ETH_MOSI 11
#define ETH_CS 10
// #define ETH_RST 15 // 9 gateway
#define ETH_RST 9 // 15 kaifaban
#define ETH_INT 14

#define PIN_SPI_SS ETH_CS
#define PIN_ETHERNET_RESET ETH_RST

// OTA 相关配置
#define OTA_PORT 8080
#define MAX_UPLOAD_SIZE (1024 * 1024)  // 1MB 最大上传大小

// WiFi 配置 (作为备用方案)
const char* ap_ssid = "ESP32-OTA";
const char* ap_password = "12345678";

// 全局变量
WebServer server(OTA_PORT);
bool otaInProgress = false;
size_t otaProgress = 0;
size_t otaTotal = 0;
bool useEthernet = true;

void getMacAddr(uint8_t *dmac)
{
  assert(esp_efuse_mac_get_default(dmac) == ESP_OK);
}

// 处理根路径请求
void handleRoot() {
  String html = "<!DOCTYPE html><html><head><title>ESP32 OTA Update</title>";
  html += "<meta charset='UTF-8'>";
  html += "<style>body{font-family:Arial;margin:40px;background:#f0f0f0;}";
  html += ".container{background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}";
  html += ".status{padding:10px;margin:10px 0;border-radius:4px;}";
  html += ".success{background:#d4edda;color:#155724;border:1px solid #c3e6cb;}";
  html += ".error{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb;}";
  html += ".info{background:#d1ecf1;color:#0c5460;border:1px solid #bee5eb;}";
  html += "input[type=file]{margin:10px 0;}";
  html += "button{background:#007bff;color:white;padding:10px 20px;border:none;border-radius:4px;cursor:pointer;}";
  html += "button:hover{background:#0056b3;}</style></head><body>";
  html += "<div class='container'><h1>ESP32 OTA 固件更新</h1>";

  if (otaInProgress) {
    html += "<div class='status info'>OTA 更新进行中... ";
    html += String((otaProgress * 100) / otaTotal) + "%</div>";
  } else {
    html += "<div class='status success'>设备就绪，可以进行OTA更新</div>";
  }

  html += "<h2>设备信息</h2>";
  if (useEthernet) {
    html += "<p><strong>连接方式:</strong> 以太网</p>";
    html += "<p><strong>IP地址:</strong> " + Ethernet.localIP().toString() + "</p>";
  } else {
    html += "<p><strong>连接方式:</strong> WiFi AP</p>";
    html += "<p><strong>IP地址:</strong> " + WiFi.softAPIP().toString() + "</p>";
  }
  html += "<p><strong>固件版本:</strong> " + String(__DATE__) + " " + String(__TIME__) + "</p>";
  html += "<p><strong>剩余空间:</strong> " + String(ESP.getFreeSketchSpace()) + " bytes</p>";

  html += "<h2>上传新固件</h2>";
  html += "<form method='POST' action='/update' enctype='multipart/form-data'>";
  html += "<input type='file' name='firmware' accept='.bin' required>";
  html += "<br><button type='submit'>开始更新</button>";
  html += "</form>";

  html += "<h2>使用说明</h2>";
  html += "<ol><li>选择编译好的.bin固件文件</li>";
  html += "<li>点击'开始更新'按钮</li>";
  html += "<li>等待更新完成，设备将自动重启</li></ol>";
  html += "</div></body></html>";

  server.send(200, "text/html", html);
}

// 处理状态请求
void handleStatus() {
  String json = "{";
  json += "\"otaInProgress\":" + String(otaInProgress ? "true" : "false") + ",";
  json += "\"progress\":" + String(otaTotal > 0 ? (otaProgress * 100) / otaTotal : 0) + ",";
  json += "\"freeSpace\":" + String(ESP.getFreeSketchSpace()) + ",";
  json += "\"version\":\"" + String(__DATE__) + " " + String(__TIME__) + "\"";
  json += "}";
  server.send(200, "application/json", json);
}

// 处理固件上传
void handleUpdate() {
  if (otaInProgress) {
    server.send(409, "text/plain", "OTA already in progress");
    return;
  }

  HTTPUpload& upload = server.upload();

  if (upload.status == UPLOAD_FILE_START) {
    Serial0.printf("开始OTA更新: %s\n", upload.filename.c_str());
    otaInProgress = true;
    otaProgress = 0;
    otaTotal = upload.totalSize;

    if (!Update.begin(UPDATE_SIZE_UNKNOWN)) {
      Serial0.printf("Update.begin() failed: %s\n", Update.errorString());
      otaInProgress = false;
      return;
    }
  }
  else if (upload.status == UPLOAD_FILE_WRITE) {
    if (Update.write(upload.buf, upload.currentSize) != upload.currentSize) {
      Serial0.printf("Update.write() failed: %s\n", Update.errorString());
      Update.abort();
      otaInProgress = false;
      return;
    }
    otaProgress += upload.currentSize;

    // 显示进度
    if (otaTotal > 0) {
      int progress = (otaProgress * 100) / otaTotal;
      Serial0.printf("OTA Progress: %d%%\r", progress);
    }
  }
  else if (upload.status == UPLOAD_FILE_END) {
    Serial0.println();
    if (Update.end(true)) {
      Serial0.println("OTA 更新成功！");
      server.send(200, "text/html",
        "<!DOCTYPE html><html><head><title>Update Success</title></head>"
        "<body><h1>固件更新成功！</h1><p>设备将在3秒后重启...</p>"
        "<script>setTimeout(function(){window.location.href='/';}, 3000);</script></body></html>");

      delay(1000);
      ESP.restart();
    } else {
      Serial0.printf("OTA 更新失败: %s\n", Update.errorString());
      server.send(500, "text/plain", "Update failed");
      otaInProgress = false;
    }
  }
}


void setup() {
  Serial0.begin(115200);
  // initialize SX1262 with default settings
  Serial0.print(F("Ota thread start\n"));

  pinMode(PIN_ETHERNET_RESET, OUTPUT);
  digitalWrite(PIN_ETHERNET_RESET, LOW); // Reset Time.
  delay(100);
  digitalWrite(PIN_ETHERNET_RESET, HIGH); // Reset Time.

  Ethernet.init(PIN_SPI_SS);
  SPI.begin(ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  Serial0.printf("SPI.begin(SCK=%d, MISO=%d, MOSI=%d, NSS=%d\n", ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  SPI.setFrequency(4000000);
  
  int status = 0;

  uint8_t mac[6];
  getMacAddr(mac); 
  mac[0] &= 0xfe;  

  Serial0.printf("Start Ethernet DHCP\n");
  status = Ethernet.begin(mac);

  if (status == 0) {
    if (Ethernet.hardwareStatus() == EthernetNoHardware) {
        Serial0.printf("Ethernet shield was not found\n");
        return;
    } else if (Ethernet.linkStatus() == LinkOFF) {
        Serial0.printf("Ethernet cable is not connected\n");
        return;
    } else {
        Serial0.printf("Unknown Ethernet error\n");
        return;
    }
} else {
    Serial0.printf("Local IP %u.%u.%u.%u\n", Ethernet.localIP()[0], Ethernet.localIP()[1], Ethernet.localIP()[2],
             Ethernet.localIP()[3]);
    Serial0.printf("Subnet Mask %u.%u.%u.%u\n", Ethernet.subnetMask()[0], Ethernet.subnetMask()[1], Ethernet.subnetMask()[2],
             Ethernet.subnetMask()[3]);
    Serial0.printf("Gateway IP %u.%u.%u.%u\n", Ethernet.gatewayIP()[0], Ethernet.gatewayIP()[1], Ethernet.gatewayIP()[2],
             Ethernet.gatewayIP()[3]);
    Serial0.printf("DNS Server IP %u.%u.%u.%u\n", Ethernet.dnsServerIP()[0], Ethernet.dnsServerIP()[1], Ethernet.dnsServerIP()[2],
             Ethernet.dnsServerIP()[3]);

    useEthernet = true;
    Serial0.printf("以太网连接成功！\n");
    Serial0.printf("访问 http://%u.%u.%u.%u:%d 进行OTA更新\n",
                   Ethernet.localIP()[0], Ethernet.localIP()[1],
                   Ethernet.localIP()[2], Ethernet.localIP()[3], OTA_PORT);
}

// 如果以太网连接失败，启用WiFi AP模式作为备用
if (!useEthernet) {
  Serial0.println("启动WiFi AP模式作为备用...");
  WiFi.softAP(ap_ssid, ap_password);
  Serial0.printf("WiFi AP已启动\n");
  Serial0.printf("SSID: %s\n", ap_ssid);
  Serial0.printf("密码: %s\n", ap_password);
  Serial0.printf("访问 http://%s:%d 进行OTA更新\n", WiFi.softAPIP().toString().c_str(), OTA_PORT);
}

// 设置WebServer路由
server.on("/", handleRoot);
server.on("/status", handleStatus);
server.on("/update", HTTP_POST, []() {
  server.send(200, "text/plain", "");
}, handleUpdate);

server.begin();
Serial0.println("HTTP服务器已启动");
}

void loop() {
  // 处理WebServer请求
  server.handleClient();

  // 检查网络连接状态
  if (useEthernet) {
    if (Ethernet.linkStatus() == LinkOFF) {
      Serial0.println("以太网连接断开");
      delay(1000);
      return;
    }
    // 维持以太网连接
    Ethernet.maintain();
  }

  delay(10);
}
