#include <Arduino.h>
#include <SPI.h>
#include <Ethernet.h>
#include <Update.h>
#include <WiFi.h>
#include <WiFiServer.h>

#define ETH_MISO 13
#define ETH_SCK 12
#define ETH_MOSI 11
#define ETH_CS 10
// #define ETH_RST 15 // 9 gateway
#define ETH_RST 9 // 15 kaifaban
#define ETH_INT 14

#define PIN_SPI_SS ETH_CS
#define PIN_ETHERNET_RESET ETH_RST

// OTA 相关配置
#define OTA_PORT 8080
#define MAX_UPLOAD_SIZE (1024 * 1024)  // 1MB 最大上传大小

// 全局变量
bool otaInProgress = false;
size_t otaProgress = 0;
size_t otaTotal = 0;

void getMacAddr(uint8_t *dmac)
{
  assert(esp_efuse_mac_get_default(dmac) == ESP_OK);
}

// 发送HTTP响应
void sendHttpResponse(EthernetClient &client, int code, const char* contentType, const char* content) {
  client.print("HTTP/1.1 ");
  client.print(code);
  client.println(code == 200 ? " OK" : " Error");
  client.print("Content-Type: ");
  client.println(contentType);
  client.print("Content-Length: ");
  client.println(strlen(content));
  client.println("Connection: close");
  client.println();
  client.print(content);
}

// 发送OTA状态页面
void sendOtaStatusPage(EthernetClient &client) {
  String html = "<!DOCTYPE html><html><head><title>ESP32 OTA Update</title>";
  html += "<meta charset='UTF-8'>";
  html += "<style>body{font-family:Arial;margin:40px;background:#f0f0f0;}";
  html += ".container{background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}";
  html += ".status{padding:10px;margin:10px 0;border-radius:4px;}";
  html += ".success{background:#d4edda;color:#155724;border:1px solid #c3e6cb;}";
  html += ".error{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb;}";
  html += ".info{background:#d1ecf1;color:#0c5460;border:1px solid #bee5eb;}";
  html += "input[type=file]{margin:10px 0;}";
  html += "button{background:#007bff;color:white;padding:10px 20px;border:none;border-radius:4px;cursor:pointer;}";
  html += "button:hover{background:#0056b3;}</style></head><body>";
  html += "<div class='container'><h1>ESP32 OTA 固件更新</h1>";

  if (otaInProgress) {
    html += "<div class='status info'>OTA 更新进行中... ";
    html += String((otaProgress * 100) / otaTotal) + "%</div>";
  } else {
    html += "<div class='status success'>设备就绪，可以进行OTA更新</div>";
  }

  html += "<h2>设备信息</h2>";
  html += "<p><strong>连接方式:</strong> 以太网</p>";
  html += "<p><strong>IP地址:</strong> " + Ethernet.localIP().toString() + "</p>";
  html += "<p><strong>固件版本:</strong> " + String(__DATE__) + " " + String(__TIME__) + "</p>";
  html += "<p><strong>剩余空间:</strong> " + String(ESP.getFreeSketchSpace()) + " bytes</p>";

  html += "<h2>上传新固件</h2>";
  html += "<form method='POST' action='/update' enctype='multipart/form-data'>";
  html += "<input type='file' name='firmware' accept='.bin' required>";
  html += "<br><button type='submit'>开始更新</button>";
  html += "</form>";

  html += "<h2>使用说明</h2>";
  html += "<ol><li>选择编译好的.bin固件文件</li>";
  html += "<li>点击'开始更新'按钮</li>";
  html += "<li>等待更新完成，设备将自动重启</li></ol>";
  html += "</div></body></html>";

  sendHttpResponse(client, 200, "text/html", html.c_str());
}

// 处理固件上传 (简化版本)
bool handleFirmwareUpload(EthernetClient &client, String &request) {
  Serial0.println("开始处理固件上传...");

  // 查找Content-Length
  int contentLengthIndex = request.indexOf("Content-Length: ");
  if (contentLengthIndex == -1) {
    sendHttpResponse(client, 400, "text/plain", "Missing Content-Length header");
    return false;
  }

  int contentLength = request.substring(contentLengthIndex + 16, request.indexOf('\r', contentLengthIndex)).toInt();
  Serial0.printf("Content-Length: %d\n", contentLength);

  if (contentLength > MAX_UPLOAD_SIZE) {
    sendHttpResponse(client, 413, "text/plain", "File too large");
    return false;
  }

  // 开始OTA更新
  otaInProgress = true;
  otaProgress = 0;
  otaTotal = contentLength;

  if (!Update.begin(UPDATE_SIZE_UNKNOWN)) {
    Serial0.printf("Update.begin() failed: %s\n", Update.errorString());
    sendHttpResponse(client, 500, "text/plain", "OTA begin failed");
    otaInProgress = false;
    return false;
  }

  Serial0.println("OTA 更新开始...");

  // 跳过HTTP头部，直接读取二进制数据
  // 这是一个简化的实现，实际应用中需要解析multipart数据
  uint8_t buffer[1024];
  size_t totalReceived = 0;
  unsigned long timeout = millis() + 30000; // 30秒超时

  while (client.connected() && totalReceived < contentLength && millis() < timeout) {
    if (client.available()) {
      int bytesRead = client.read(buffer, min(sizeof(buffer), contentLength - totalReceived));
      if (bytesRead > 0) {
        // 写入固件数据
        if (Update.write(buffer, bytesRead) != bytesRead) {
          Serial0.printf("Update.write() failed: %s\n", Update.errorString());
          Update.abort();
          sendHttpResponse(client, 500, "text/plain", "Write failed");
          otaInProgress = false;
          return false;
        }
        totalReceived += bytesRead;
        otaProgress = totalReceived;

        // 显示进度
        if (otaTotal > 0) {
          int progress = (otaProgress * 100) / otaTotal;
          Serial0.printf("OTA Progress: %d%%\r", progress);
        }
      }
    }
    delay(1);
  }

  Serial0.println();

  if (totalReceived < contentLength) {
    Serial0.println("接收数据不完整");
    Update.abort();
    sendHttpResponse(client, 500, "text/plain", "Incomplete upload");
    otaInProgress = false;
    return false;
  }

  if (Update.end(true)) {
    Serial0.println("OTA 更新成功！");
    sendHttpResponse(client, 200, "text/html",
      "<!DOCTYPE html><html><head><title>Update Success</title></head>"
      "<body><h1>固件更新成功！</h1><p>设备将在3秒后重启...</p>"
      "<script>setTimeout(function(){window.location.href='/';}, 3000);</script></body></html>");

    delay(1000);
    ESP.restart();
    return true;
  } else {
    Serial0.printf("OTA 更新失败: %s\n", Update.errorString());
    sendHttpResponse(client, 500, "text/plain", "Update failed");
    otaInProgress = false;
    return false;
  }
}


void setup() {
  Serial0.begin(115200);
  // initialize SX1262 with default settings
  Serial0.print(F("Ota thread start\n"));

  pinMode(PIN_ETHERNET_RESET, OUTPUT);
  digitalWrite(PIN_ETHERNET_RESET, LOW); // Reset Time.
  delay(100);
  digitalWrite(PIN_ETHERNET_RESET, HIGH); // Reset Time.

  Ethernet.init(PIN_SPI_SS);
  SPI.begin(ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  Serial0.printf("SPI.begin(SCK=%d, MISO=%d, MOSI=%d, NSS=%d\n", ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  SPI.setFrequency(4000000);
  
  int status = 0;

  uint8_t mac[6];
  getMacAddr(mac); 
  mac[0] &= 0xfe;  

  Serial0.printf("Start Ethernet DHCP\n");
  status = Ethernet.begin(mac);

  if (status == 0) {
    if (Ethernet.hardwareStatus() == EthernetNoHardware) {
        Serial0.printf("Ethernet shield was not found\n");
        return;
    } else if (Ethernet.linkStatus() == LinkOFF) {
        Serial0.printf("Ethernet cable is not connected\n");
        return;
    } else {
        Serial0.printf("Unknown Ethernet error\n");
        return;
    }
} else {
    Serial0.printf("Local IP %u.%u.%u.%u\n", Ethernet.localIP()[0], Ethernet.localIP()[1], Ethernet.localIP()[2],
             Ethernet.localIP()[3]);
    Serial0.printf("Subnet Mask %u.%u.%u.%u\n", Ethernet.subnetMask()[0], Ethernet.subnetMask()[1], Ethernet.subnetMask()[2],
             Ethernet.subnetMask()[3]);
    Serial0.printf("Gateway IP %u.%u.%u.%u\n", Ethernet.gatewayIP()[0], Ethernet.gatewayIP()[1], Ethernet.gatewayIP()[2],
             Ethernet.gatewayIP()[3]);
    Serial0.printf("DNS Server IP %u.%u.%u.%u\n", Ethernet.dnsServerIP()[0], Ethernet.dnsServerIP()[1], Ethernet.dnsServerIP()[2],
             Ethernet.dnsServerIP()[3]);

    Serial0.printf("以太网连接成功！\n");
    Serial0.printf("访问 http://%u.%u.%u.%u:%d 进行OTA更新\n",
                   Ethernet.localIP()[0], Ethernet.localIP()[1],
                   Ethernet.localIP()[2], Ethernet.localIP()[3], OTA_PORT);
}

Serial0.println("HTTP服务器已启动,等待连接...");
}

void loop() {
  // 检查以太网连接状态
  if (Ethernet.linkStatus() == LinkOFF) {
    Serial0.println("以太网连接断开");
    delay(1000);
    return;
  }

  // 创建一个简单的TCP服务器监听连接
  EthernetClient client;

  // 使用一个静态的EthernetServer来监听连接
  // 注意：这里我们需要找到一个不使用抽象类的方法
  static bool serverInitialized = false;

  if (!serverInitialized) {
    // 初始化服务器监听
    serverInitialized = true;
    Serial0.printf("开始监听端口 %d\n", OTA_PORT);
  }

  // 这里需要实现一个替代方案来监听TCP连接
  // 由于EthernetServer是抽象类，我们需要使用其他方法

  // 临时解决方案：使用延时和维护连接
  Ethernet.maintain();
  delay(100);
}
