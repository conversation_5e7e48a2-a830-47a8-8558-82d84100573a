#include <Arduino.h>
#include <SPI.h>
#include <Ethernet.h>
#include <Update.h>

#define ETH_MISO 13
#define ETH_SCK 12
#define ETH_MOSI 11
#define ETH_CS 10
// #define ETH_RST 15 // 9 gateway
#define ETH_RST 9 // 15 kaifaban
#define ETH_INT 14

#define PIN_SPI_SS ETH_CS
#define PIN_ETHERNET_RESET ETH_RST

// OTA 服务器配置
const char* OTA_SERVER = "th.ota.roulink.com";
const int OTA_PORT = 80;
const char* OTA_PATH = "http://th.ota.roulink.com/download/firmware.bin";

// 全局变量
bool otaInProgress = false;
size_t otaProgress = 0;
size_t otaTotal = 0;

void getMacAddr(uint8_t *dmac) {
  assert(esp_efuse_mac_get_default(dmac) == ESP_OK);
}

// 连接到OTA服务器并获取固件信息
bool connectToOtaServer() {
  Serial0.println("正在连接到OTA服务器...");

  EthernetClient client;

  if (client.connect(OTA_SERVER, OTA_PORT)) {
    Serial0.printf("已连接到 %s:%d\n", OTA_SERVER, OTA_PORT);

//     // 发送HTTP GET请求
//     client.print("GET ");
//     client.print(OTA_PATH);
//     client.println(" HTTP/1.1");
//     client.print("Host: ");
//     client.println(OTA_SERVER);
//     client.println("User-Agent: ESP32-OTA-Client/1.0");
//     client.println("Connection: close");
//     client.println();

//     Serial0.println("HTTP请求已发送,等待响应...");

//     // 等待响应
//     unsigned long timeout = millis() + 10000; // 10秒超时
//     while (client.connected() && millis() < timeout) {
//       if (client.available()) {
//         String line = client.readStringUntil('\n');
//         Serial0.println("响应: " + line);

//         // 检查HTTP状态码
//         if (line.startsWith("HTTP/1.1 200")) {
//           Serial0.println("✅ 服务器响应成功 (200 OK)");
//         } else if (line.startsWith("HTTP/1.1")) {
//           Serial0.println("⚠️ 服务器响应: " + line);
//         }

//         // 如果是空行，表示头部结束
//         if (line.length() <= 1) {
//           Serial0.println("--- HTTP头部结束，开始读取内容 ---");

//           // 读取响应内容
//           String content = "";
//           int contentLines = 0;
//           while (client.available() && contentLines < 20) { // 限制读取行数
//             String contentLine = client.readStringUntil('\n');
//             content += contentLine + "\n";
//             contentLines++;
//           }

//           if (content.length() > 0) {
//             Serial0.println("响应内容:");
//             Serial0.println(content);
//           }
//           break;
//         }
//       }
//     }

//     client.stop();
//     Serial0.println("连接已关闭");
//     return true;

//   } else {
//     Serial0.printf(" 无法连接到 %s:%d\n", OTA_SERVER, OTA_PORT);
//     return false;
//   }
// }

// // 下载固件文件
// bool downloadFirmware(const char* firmwarePath) {
//   Serial0.printf("正在下载固件: %s\n", firmwarePath);

//   EthernetClient client;

//   if (client.connect(OTA_SERVER, OTA_PORT)) {
//     Serial0.printf("已连接到 %s:%d\n", OTA_SERVER, OTA_PORT);

//     // 发送HTTP GET请求下载固件
//     client.print("GET ");
//     client.print(firmwarePath);
//     client.println(" HTTP/1.1");
//     client.print("Host: ");
//     client.println(OTA_SERVER);
//     client.println("User-Agent: ESP32-OTA-Client/1.0");
//     client.println("Connection: close");
//     client.println();

//     Serial0.println("固件下载请求已发送...");

//     // 读取HTTP响应头
//     bool headersParsed = false;
//     int contentLength = 0;
//     unsigned long timeout = millis() + 10000;

//     while (client.connected() && millis() < timeout && !headersParsed) {
//       if (client.available()) {
//         String line = client.readStringUntil('\n');
//         line.trim();

//         if (line.startsWith("HTTP/1.1 200")) {
//           Serial0.println("✅ 固件文件找到");
//         } else if (line.startsWith("HTTP/1.1")) {
//           Serial0.println("❌ 服务器响应: " + line);
//           client.stop();
//           return false;
//         } else if (line.startsWith("Content-Length:")) {
//           contentLength = line.substring(15).toInt();
//           Serial0.printf("固件大小: %d bytes\n", contentLength);
//         } else if (line.length() == 0) {
//           headersParsed = true;
//           Serial0.println("开始下载固件数据...");
//         }
//       }
//     }

//     if (!headersParsed) {
//       Serial0.println("❌ 解析HTTP头部超时");
//       client.stop();
//       return false;
//     }

//     // 开始OTA更新
//     if (!Update.begin(contentLength > 0 ? contentLength : UPDATE_SIZE_UNKNOWN)) {
//       Serial0.printf("❌ Update.begin() 失败: %s\n", Update.errorString());
//       client.stop();
//       return false;
//     }

//     otaInProgress = true;
//     otaProgress = 0;
//     otaTotal = contentLength;

//     Serial0.println("开始写入固件...");

//     // 读取并写入固件数据
//     uint8_t buffer[1024];
//     size_t totalWritten = 0;
//     timeout = millis() + 60000; // 60秒超时

//     while (client.connected() && millis() < timeout) {
//       if (client.available()) {
//         int bytesRead = client.read(buffer, sizeof(buffer));
//         if (bytesRead > 0) {
//           if (Update.write(buffer, bytesRead) != bytesRead) {
//             Serial0.printf("❌ Update.write() 失败: %s\n", Update.errorString());
//             Update.abort();
//             otaInProgress = false;
//             client.stop();
//             return false;
//           }

//           totalWritten += bytesRead;
//           otaProgress = totalWritten;

//           // 显示进度
//           if (contentLength > 0) {
//             int progress = (totalWritten * 100) / contentLength;
//             Serial0.printf("下载进度: %d%% (%d/%d bytes)\r", progress, totalWritten, contentLength);
//           } else {
//             Serial0.printf("已下载: %d bytes\r", totalWritten);
//           }
//         }
//       }
//     }

//     Serial0.println();
//     client.stop();

//     // 完成OTA更新
//     if (Update.end(true)) {
//       Serial0.printf("✅ 固件更新成功! 总共写入 %d bytes\n", totalWritten);
//       Serial0.println("设备将在3秒后重启...");
//       delay(3000);
//       ESP.restart();
//       return true;
//     } else {
//       Serial0.printf("❌ Update.end() 失败: %s\n", Update.errorString());
//       otaInProgress = false;
//       return false;
//     }

  } else {
    Serial0.printf("❌ 无法连接到 %s:%d\n", OTA_SERVER, OTA_PORT);
    return false;
  }
}


void setup() {
  Serial0.begin(115200);
  // initialize SX1262 with default settings
  Serial0.print(F("Ota thread start\n"));

  pinMode(PIN_ETHERNET_RESET, OUTPUT);
  digitalWrite(PIN_ETHERNET_RESET, LOW); // Reset Time.
  delay(100);
  digitalWrite(PIN_ETHERNET_RESET, HIGH); // Reset Time.

  Ethernet.init(PIN_SPI_SS);
  SPI.begin(ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  Serial0.printf("SPI.begin(SCK=%d, MISO=%d, MOSI=%d, NSS=%d\n", ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  SPI.setFrequency(4000000);
  
  int status = 0;

  uint8_t mac[6];
  getMacAddr(mac); 
  mac[0] &= 0xfe;  

  Serial0.printf("Start Ethernet DHCP\n");
  status = Ethernet.begin(mac);

  if (status == 0) {
    if (Ethernet.hardwareStatus() == EthernetNoHardware) {
        Serial0.printf("Ethernet shield was not found\n");
        return;
    } else if (Ethernet.linkStatus() == LinkOFF) {
        Serial0.printf("Ethernet cable is not connected\n");
        return;
    } else {
        Serial0.printf("Unknown Ethernet error\n");
        return;
    }
} else {
    Serial0.printf("Local IP %u.%u.%u.%u\n", Ethernet.localIP()[0], Ethernet.localIP()[1], Ethernet.localIP()[2],
             Ethernet.localIP()[3]);
    Serial0.printf("Subnet Mask %u.%u.%u.%u\n", Ethernet.subnetMask()[0], Ethernet.subnetMask()[1], Ethernet.subnetMask()[2],
             Ethernet.subnetMask()[3]);
    Serial0.printf("Gateway IP %u.%u.%u.%u\n", Ethernet.gatewayIP()[0], Ethernet.gatewayIP()[1], Ethernet.gatewayIP()[2],
             Ethernet.gatewayIP()[3]);
    Serial0.printf("DNS Server IP %u.%u.%u.%u\n", Ethernet.dnsServerIP()[0], Ethernet.dnsServerIP()[1], Ethernet.dnsServerIP()[2],
             Ethernet.dnsServerIP()[3]);

    Serial0.printf("以太网连接成功！\n");
    Serial0.printf("本地IP: %u.%u.%u.%u\n",
                   Ethernet.localIP()[0], Ethernet.localIP()[1],
                   Ethernet.localIP()[2], Ethernet.localIP()[3]);

    // 连接到OTA服务器
    Serial0.println("\n=== 开始连接OTA服务器 ===");
    connectToOtaServer();
}
}

void loop() {
  // 检查以太网连接状态
  if (Ethernet.linkStatus() == LinkOFF) {
    Serial0.println("以太网连接断开");
    delay(1000);
    return;
  }

  // 每30秒尝试连接一次OTA服务器
  static unsigned long lastOtaCheck = 0;
  if (millis() - lastOtaCheck > 30000) {
    lastOtaCheck = millis();

    Serial0.println("\n=== 定期检查OTA服务器 ===");
    if (connectToOtaServer()) {
      Serial0.println("OTA服务器连接测试成功");

      // 如果需要，可以在这里下载固件
      // 例如：downloadFirmware("/firmware.bin");
    }
  }

  // 维持以太网连接
  Ethernet.maintain();
  delay(1000);
}
